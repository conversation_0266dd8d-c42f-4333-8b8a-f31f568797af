<script lang="ts">
	import { twJoin } from 'tailwind-merge';

	// Mobile navigation tab bar
	import { languageModal, menuItems } from './menuData.svelte';
	import LanguagePicker from '@lib/components/LanguagePicker.svelte';

	const itemStyle = `flex flex-col items-center w-full h-full gap-2 text-center text-xs overflow-hidden rounded-xl py-2 px-3 text-sm font-medium text-gray-400 hover:bg-gray-100 hover:text-gray-700`;
</script>

<LanguagePicker modalState={languageModal.state} onClose={() => (languageModal.state = false)}
></LanguagePicker>

<div class="min-h-[4.5rem]">
	<div class="fixed left-0 right-0 top-auto bottom-0 z-30 mt-6 w-full p-2 lg:hidden">
		<nav
			class="mx-auto flex h-full max-w-screen-2xl items-start justify-center rounded-xl bg-white/80 p-1 shadow-sm backdrop-blur-lg"
		>
			{#each menuItems as item}
				{#if item.href}
					<a href={item.href} class={twJoin(itemStyle)}>
						<item.icon width="24" height="24" class=" text-gray-500" />
						{item.label}
					</a>
				{:else if item.type === 'button'}
					<button onclick={item.onClick} class={twJoin(itemStyle)}>
						<item.icon width="24" height="24" class=" text-gray-500" />
						{item.label}
					</button>
				{/if}
			{/each}
		</nav>
	</div>
</div>
