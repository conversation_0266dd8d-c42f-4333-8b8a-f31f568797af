{"name": "open-gate-vpn", "version": "0.0.1", "devDependencies": {"@iconify/json": "^2.2.356", "@inlang/cli": "^3.0.12", "@inlang/paraglide-js": "2.0.12", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.22.3", "@sveltejs/vite-plugin-svelte": "^5.1.0", "autoprefixer": "^10.4.21", "flowbite": "^3.1.2", "flowbite-svelte": "^1.8.6", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.35.5", "svelte-check": "^4.2.2", "typescript": "^5.8.3", "unplugin-icons": "^22.1.0", "vite": "^6.3.5", "vite-plugin-devtools-json": "^0.1.1"}, "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "machine-translate": "inlang machine translate --project project.inlang"}, "type": "module", "dependencies": {"@tailwindcss/vite": "^4.1.11", "class-variance-authority": "^0.7.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}}