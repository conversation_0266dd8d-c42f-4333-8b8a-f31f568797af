<script lang="ts">
	import LanguagePicker from '@lib/components/LanguagePicker.svelte';
	import { page } from '$app/state';
	import { m } from '@lib/paraglide/messages.js';
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { MediaQuery } from 'svelte/reactivity';
	import { twMerge } from 'tailwind-merge';
	import { languageModal, menuItems } from './menuData.svelte';

	import Logo from '$lib/assets/logo.svg';

	// Icons
	import IconoirXmark from '~icons/iconoir/xmark';
	import HeroiconsBars3BottomLeft from '~icons/heroicons/bars-3-bottom-left';

	let large = $state({} as MediaQuery);
	let isSidebarOpen = $state(false);
	let activeUrl = $state(page.url.pathname);
	let sidebarRef: HTMLElement | null = null;
	let breakpointLg: string;

	const menuItemStyle =
		'flex items-center gap-2 w-full rounded-xl p-2 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700';

	onMount(() => {
		const style = getComputedStyle(document.documentElement);
		breakpointLg = style.getPropertyValue('--breakpoint-lg').trim();
		large = new MediaQuery(`min-width: ${breakpointLg}`);

		const handleClickOutside = (event: MouseEvent) => {
			if (isSidebarOpen && sidebarRef && !sidebarRef.contains(event.target as Node)) {
				isSidebarOpen = false;
			}
		};

		document.addEventListener('mousedown', handleClickOutside);

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	});

	$effect(() => {
		activeUrl = page.url.pathname;
	});
	$effect(() => {
		console.log(large.current);
	});

	function toggleSidebar() {
		isSidebarOpen = !isSidebarOpen;
	}
</script>

{#snippet button()}
	<button
		onclick={toggleSidebar}
		class="inline-flex w-fit items-center rounded-lg bg-blue-700 p-2 text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none lg:hidden dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
		aria-controls="sidebar-multi-level"
		aria-expanded={isSidebarOpen}
	>
		{#if isSidebarOpen}<IconoirXmark class="h-5 w-5" />
		{:else}
			<HeroiconsBars3BottomLeft class="h-5 w-5" />
		{/if}
		<!-- <span class="sr-only">{m['sidebar.open']()}</span> -->
	</button>
{/snippet}

<LanguagePicker modalState={languageModal.state} onClose={() => (languageModal.state = false)}
></LanguagePicker>

<div class="flex items-center justify-between overflow-hidden lg:w-0">
	<!-- <div class="block lg:hidden text-xl font-semibold text-slate-700">OpenGate VPN</div> -->
	<img src={Logo} alt="OpenGate VPN" class="h-10" />
	<!-- {@render button()} -->
</div>

<div class="relative max-lg:contents">
	<aside
		bind:this={sidebarRef}
		class={twMerge(
			'sticky top-10 left-0 z-40  w-64 transform bg-gradient-to-b from-purple-50 via-white to-white shadow-sm transition-transform duration-300 ease-in-out max-lg:fixed max-lg:hidden max-lg:h-full max-lg:w-full lg:rounded-2xl',
			isSidebarOpen ? 'top-0 translate-x-0' : '-translate-x-[-100vw] lg:translate-x-0'
		)}
		aria-label="Sidebar"
	>
		<div class="h-full overflow-y-auto px-3 py-4 dark:bg-gray-800">
			<div class="mb-4 flex items-center justify-between">
				<!-- <div class="text-xl font-semibold text-slate-700">OpenGate VPN</div> -->
				<img src={Logo} alt="OpenGate VPN" class="h-10" />
				{@render button()}
			</div>
			<ul class="space-y-2 font-medium">
				{#each menuItems as item}
					<li>
						{#if item.href}
							<a href={item.href} class={menuItemStyle}>
								<item.icon width="24" height="24" class="mr-2 text-gray-500 dark:text-gray-400" />
								{item.label}
							</a>
						{:else if item.type === 'button'}
							<button onclick={item.onClick} class={`${menuItemStyle} w-full justify-start`}>
								<item.icon width="24" height="24" class="mr-2 text-gray-500 dark:text-gray-400" />
								{item.label}
							</button>
						{:else if item.type === 'divider'}
							<div class={item.class}></div>
						{/if}
					</li>
				{/each}
			</ul>
		</div>
	</aside>
</div>
