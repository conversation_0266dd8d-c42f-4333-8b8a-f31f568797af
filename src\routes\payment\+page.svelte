<script lang="ts">
	import Sidebar from '@lib/components/navigation/Sidebar.svelte';
	import TabNavigation from '@lib/components/navigation/TabNavigation.svelte';
	import { m } from '@lib/paraglide/messages';
	import { <PERSON><PERSON>, <PERSON><PERSON>, Heading } from 'flowbite-svelte';
	import { twMerge } from 'tailwind-merge';

	import SolarShieldWarningLineDuotone from '~icons/solar/shield-warning-line-duotone';

	// Mock data based on OpenAPI schemas
	const mockTariff = {
		id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
		title: 'BlancVPN Premium Plan',
		duration: 6,
		duration_type: 'months',
		prices: [
			{
				currency: 'usd',
				value: '36.73'
			}
		]
	};

	const mockOrder = {
		id: 'order-123',
		tariff_id: mockTariff.id,
		state: 'pending',
		payment_links: [
			{
				provider: 'yookassa_cards',
				url: 'https://payment.example.com/cards'
			},
			{
				provider: 'yookassa_sbp',
				url: 'https://payment.example.com/sbp'
			},
			{
				provider: 'cryptobot',
				url: 'https://payment.example.com/crypto'
			}
		]
	};

	const mockPaymentMethods = [
		{
			type: 'bank_card',
			title: 'Credit or debit card',
			providers: ['visa', 'mastercard', 'amex', 'discover'],
			available: true
		},
		{
			type: 'crypto',
			title: 'Cryptocurrencies',
			providers: ['bitcoin', 'ethereum', 'usdt'],
			available: true
		},
		{
			type: 'sbp',
			title: 'Fast Payment System',
			providers: ['sbp'],
			available: true,
			region: 'Russia'
		}
	];

	const includedFeatures = [
		'VPN for all your devices',
		'Unlimited traffic. Fast servers',
		'Locations on all continents',
		'Friendly support 24/7'
	];

	let selectedPaymentMethod = $state(0);

	function selectPaymentMethod(index: number) {
		selectedPaymentMethod = index;
	}

	function formatPrice(price: string, currency: string): string {
		const symbol = currency === 'usd' ? '$' : currency === 'rub' ? '₽' : currency;
		return `${symbol}${price}`;
	}

	function calculateDiscount() {
		const originalPrice = 47.09;
		const currentPrice = parseFloat(mockTariff.prices[0].value);
		const discountPercent = Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
		return { discountPercent, originalPrice };
	}

	const discount = calculateDiscount();
</script>

<div class="container mb-6 flex w-full max-w-screen-2xl flex-col gap-10 pt-10 lg:flex-row">
	<Sidebar></Sidebar>
	<div class="relative w-full">
		<Alert
			color="secondary"
			class="mx-auto mb-4 flex w-fit items-center justify-center gap-2 rounded-full bg-slate-200 py-2"
		>
			<SolarShieldWarningLineDuotone class="inline-block shrink-0" width="24" height="24" />
			<span class="font-medium">{m['common.not-protected']()}</span>
		</Alert>

		<div class="mx-auto max-w-screen-xl space-y-8">
			<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
				<!-- Left Column - Payment Methods -->
				<div class="space-y-6">
					<Heading tag="h2" class="text-xl font-semibold text-slate-700"
						>Select a payment method</Heading
					>

					<div class="space-y-4">
						{#each mockPaymentMethods as method, index}
							<button
								class={twMerge(
									'cursor-pointer rounded-lg border bg-white p-6 transition-colors',
									selectedPaymentMethod === index
										? 'border-purple-500 bg-blue-50'
										: 'border-gray-200 hover:border-gray-300'
								)}
								onclick={() => selectPaymentMethod(index)}
							>
								<div class="flex items-center justify-between">
									<div class="flex items-center gap-3">
										<input
											type="radio"
											checked={selectedPaymentMethod === index}
											class="text-purple-600"
										/>
										<span class="text-lg font-medium text-slate-900">{method.title}</span>
										{#if method.region}
											<span class="text-sm text-gray-500">({method.region})</span>
										{/if}
									</div>
									<div class="flex items-center gap-2">
										{#if method.type === 'bank_card'}
											<div class="flex h-5 w-8 items-center justify-center rounded-sm bg-purple-600">
												<span class="text-xs font-bold text-white">VISA</span>
											</div>
											<div class="h-5 w-8 rounded-sm bg-red-500"></div>
											<div class="h-5 w-8 rounded-sm bg-blue-400"></div>
											<div class="h-5 w-8 rounded-sm bg-black"></div>
										{:else if method.type === 'crypto'}
											<div class="h-6 w-6 rounded-full bg-orange-500"></div>
											<div class="h-6 w-6 rounded-full bg-green-500"></div>
											<div class="h-6 w-6 rounded-full bg-gray-800"></div>
										{:else if method.type === 'sbp'}
											<div class="flex h-5 w-8 items-center justify-center rounded-sm bg-green-600">
												<span class="text-xs text-white">СБП</span>
											</div>
										{/if}
									</div>
								</div>

								{#if selectedPaymentMethod === index && method.type === 'bank_card'}
									<div class="mt-6 text-sm leading-relaxed text-gray-600">
										By completing your purchase, you agree to our <a
											href="#"
											class="text-purple-600 underline">Terms of Service</a
										>,
										<a href="#" class="text-purple-600 underline">Privacy Policy</a>, and
										auto-renewal. Your subscription renews automatically at our standard rate. Promo
										discounts apply only to your first payment. You can
										<a href="#" class="text-purple-600 underline">manage or cancel</a>
										subscription anytime through your
										<a href="#" class="text-purple-600 underline">account</a>.
									</div>

									<div class="mt-4 text-sm text-gray-600">
										OpenGate VPN is provided by Yadda OÜ.
									</div>
								{/if}
							</button>
						{/each}
					</div>

					{#if selectedPaymentMethod !== null}
						<div class="flex items-center justify-between">
							<Button
								color="blue"
								class="px-8 py-3 font-medium"
								href={mockOrder.payment_links[selectedPaymentMethod]?.url}
							>
								Proceed to payment
							</Button>
							<div class="flex items-center gap-2 text-sm text-gray-600">
								<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
										clip-rule="evenodd"
									></path>
								</svg>
								Risk-free: 30-day refund policy
							</div>
						</div>
					{/if}

					<div class="pt-4">
						<a href="#" class="flex items-center gap-2 text-gray-600 hover:text-gray-800">
							Subscription terms
							<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
								<path
									fill-rule="evenodd"
									d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
									clip-rule="evenodd"
								></path>
							</svg>
						</a>
					</div>
				</div>

				<!-- Right Column - Order Summary -->
				<div class="space-y-6">
					<Heading tag="h2" class="text-2xl font-semibold text-slate-900">Your order</Heading>

					<div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div class="mb-4 flex items-center justify-between">
							<div class="flex items-center gap-3">
								<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500">
									<span class="text-sm font-bold text-white">O</span>
								</div>
								<span class="text-lg font-medium"
									>{mockTariff.title} for {mockTariff.duration} {mockTariff.duration_type}</span
								>
							</div>
							<span class="text-2xl font-bold text-slate-900"
								>{formatPrice(mockTariff.prices[0].value, mockTariff.prices[0].currency)}</span
							>
						</div>

						<div class="mb-6 flex items-center justify-between">
							<span class="rounded bg-blue-100 px-3 py-1 text-sm font-medium text-purple-800">
								Discount {discount.discountPercent}%
							</span>
							<span class="text-gray-500 line-through">${discount.originalPrice}</span>
						</div>

						<div class="mb-6 text-center">
							<a href="#" class="text-sm text-purple-600 hover:text-purple-800">Have a coupon?</a>
						</div>

						<!-- Upgrade Option -->
						<div class="mb-6 rounded-lg bg-purple-500 p-4 text-white">
							<div class="flex items-center justify-between">
								<div>
									<div class="text-lg font-semibold">Get 50% discount</div>
									<div class="text-purple-100">Switch to a 1 year plan</div>
								</div>
								<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
										clip-rule="evenodd"
									></path>
								</svg>
							</div>
						</div>

						<!-- Included Features -->
						<div class="mb-6 space-y-4">
							<Heading tag="h3" class="font-semibold text-slate-900"
								>Included in the subscription</Heading
							>

							<div class="space-y-3">
								{#each includedFeatures as feature}
									<div class="flex items-center gap-3">
										<svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
											<path
												fill-rule="evenodd"
												d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
												clip-rule="evenodd"
											></path>
										</svg>
										<span class="text-gray-700">{feature}</span>
									</div>
								{/each}
							</div>
						</div>

						<!-- Money-back Guarantee -->
						<div class="flex items-center gap-3 rounded-lg bg-blue-50 p-4">
							<div class="flex h-10 w-10 items-center justify-center rounded-full bg-purple-500">
								<svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									></path>
								</svg>
							</div>
							<span class="font-medium text-slate-900">30-day money-back guarantee</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<TabNavigation></TabNavigation>
