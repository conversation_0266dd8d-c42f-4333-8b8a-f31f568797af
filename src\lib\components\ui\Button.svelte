<script lang="ts">
/**
 * Button Component
 *
 * A versatile button component that can be used as a button or a link.
 * Supports various styles, sizes, and icon positions.
 */

import { twMerge } from "tailwind-merge";
import { cva, type VariantProps } from "class-variance-authority";

type IconPosition = "left" | "right";
type ButtonVariant = "primary" | "secondary" | "tertiary" | "blue";
type ButtonSize = "small" | "default" | "big";

interface Props {
  variant?: ButtonVariant;
  size?: ButtonSize;
  shadow?: boolean;
  icon?: boolean;
  iconPosition?: IconPosition;
  disabled?: boolean;
  class?: string;
  href?: string;
  type?: "button" | "submit" | "reset";
  ariaLabel?: string;
  target?: string;
  rel?: string;
  onclick?: (event: Event) => void;
  children?: import('svelte').Snippet;
  iconSnippet?: import('svelte').Snippet;
}

let {
  variant = "primary",
  size = "default",
  shadow = false,
  icon = false,
  iconPosition = "right",
  disabled = false,
  class: className = "",
  href,
  type = "button",
  ariaLabel,
  target,
  rel,
  onclick,
  children,
  iconSnippet,
  ...restProps
}: Props = $props();

// Define the button variants using CVA
const buttonVariants = cva(
  [
    "flex",
    "items-center",
    "justify-center",
    "min-h-[42px]",
    "rounded-lg",
    "font-medium",
    "text-base",
    "leading-none",
    "transition-colors",
    "duration-300",
    "ease-in-out",
  ],
  {
    variants: {
      variant: {
        primary: [
          "bg-blue-600",
          "text-white",
          '[text-shadow:0px_2px_2px_#00000050]',
          "hover:bg-blue-700",
          "active:bg-blue-800",
        ],
        secondary: [
          "bg-white",
          "border",
          "border-gray-200",
          "text-gray-900",
          "hover:bg-gray-100",
          "active:bg-gray-200",
        ],
        tertiary: [
          "text-blue-600",
          "hover:text-blue-800",
          "active:text-blue-900",
        ],
        blue: [
          "bg-blue-600",
          "text-white",
          "hover:bg-blue-500",
          "active:bg-blue-700",
        ],
      },
      size: {
        small: "px-4 py-2 text-sm gap-1",
        default: "px-4 py-2.5 gap-2",
        big: "px-10 py-4 max-lg:px-6 text-xl font-medium gap-4",
      },
      shadow: {
        true: "",
        false: "",
      },
      iconPosition: {
        right: "flex-row",
        left: "flex-row-reverse",
      },
      disabled: {
        true: "opacity-50 cursor-not-allowed pointer-events-none",
        false: "",
      },
    },
    compoundVariants: [
      {
        variant: "primary",
        size: "big",
        shadow: true,
        class: "shadow-[0_4px_16px_0px] shadow-blue-500/50",
      },
      {
        variant: "primary",
        size: "default",
        shadow: true,
        class: "shadow-[0_2px_12px_0px] shadow-blue-500/50",
      },
      {
        variant: "secondary",
        size: "default",
        shadow: true,
        class: "shadow-[0_2px_12px_0px] shadow-gray-200",
      },
      {
        variant: "blue",
        size: "default",
        shadow: true,
        class: "shadow-[0_2px_12px_0px] shadow-blue-500/50",
      },
    ],
    defaultVariants: {
      variant: "primary",
      size: "default",
      shadow: false,
      iconPosition: "right",
      disabled: false,
    },
  },
);

// Compute classes using the CVA configuration
const classes = $derived(
  twMerge(
    buttonVariants({
      variant,
      size,
      shadow,
      iconPosition,
      disabled,
      class: className,
    }),
  )
);

// Determine if it should be a link
const isLink = $derived(href && !disabled);

// Prepare link attributes
const linkTarget = $derived(
  href?.startsWith("http") && !target ? "_blank" : target
);
const linkRel = $derived(
  href?.startsWith("http") && !rel ? "noopener noreferrer" : rel
);
</script>

{#if isLink}
  <!-- svelte-ignore a11y_consider_explicit_label -->
  <a
    {href}
    class={classes}
    target={linkTarget}
    rel={linkRel}
    aria-label={ariaLabel}
    {...restProps}
  >
    {@render children?.()}
    {#if icon && iconSnippet}
      <div class="shrink-0">
        {@render iconSnippet()}
      </div>
    {/if}
  </a>
{:else}
  <!-- svelte-ignore a11y_consider_explicit_label -->
  <button
    {type}
    {disabled}
    class={classes}
    aria-label={ariaLabel}
    onclick={onclick}
    {...restProps}
  >
    {@render children?.()}
    {#if icon && iconSnippet}
      <div class="shrink-0">
        {@render iconSnippet()}
      </div>
    {/if}
  </button>
{/if}