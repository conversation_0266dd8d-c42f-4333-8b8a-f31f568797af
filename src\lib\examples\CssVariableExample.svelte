<script>
	import { onMount } from 'svelte';
	
	// Initialize with default values
	let primaryColor = $state('');
	let fontSize = $state('');
	let spacing = $state('');
	
	// Function to get CSS variable value
	function getCssVariable(variableName) {
		const style = getComputedStyle(document.documentElement);
		return style.getPropertyValue(variableName).trim();
	}
	
	onMount(() => {
		// Access CSS variables after component is mounted
		primaryColor = getCssVariable('--primary-color');
		fontSize = getCssVariable('--font-size');
		spacing = getCssVariable('--spacing');
		
		// You can also listen for changes in CSS variables
		// This is useful if your theme can change dynamically
		const observer = new MutationObserver(() => {
			primaryColor = getCssVariable('--primary-color');
			fontSize = getCssVariable('--font-size');
			spacing = getCssVariable('--spacing');
		});
		
		// Observe the document element for attribute changes
		observer.observe(document.documentElement, { 
			attributes: true,
			attributeFilter: ['style', 'class'] 
		});
		
		return () => {
			// Clean up the observer when component is destroyed
			observer.disconnect();
		};
	});
	
	// Function to change theme (for demonstration)
	function toggleTheme() {
		const root = document.documentElement;
		if (root.classList.contains('dark-theme')) {
			root.classList.remove('dark-theme');
		} else {
			root.classList.add('dark-theme');
		}
	}
</script>

<style>
	:global(:root) {
		--primary-color: #3498db;
		--font-size: 16px;
		--spacing: 1rem;
	}
	
	:global(:root.dark-theme) {
		--primary-color: #9b59b6;
		--font-size: 18px;
		--spacing: 1.2rem;
	}
	
	.example-container {
		padding: var(--spacing);
		background-color: #f5f5f5;
		border-radius: 8px;
		margin-bottom: 20px;
	}
	
	.color-sample {
		width: 50px;
		height: 50px;
		border-radius: 4px;
		margin-bottom: 10px;
	}
	
	.variable-display {
		margin-bottom: 10px;
		font-family: monospace;
	}
</style>

<div class="example-container">
	<h2>CSS Variables in Svelte 5</h2>
	
	<div class="color-sample" style="background-color: {primaryColor}"></div>
	
	<div class="variable-display">
		--primary-color: {primaryColor}
	</div>
	
	<div class="variable-display">
		--font-size: {fontSize}
	</div>
	
	<div class="variable-display">
		--spacing: {spacing}
	</div>
	
	<button on:click={toggleTheme}>Toggle Theme</button>
	
	<p style="font-size: {fontSize}; margin-top: {spacing};">
		This text uses the font size from CSS variables.
	</p>
</div>
