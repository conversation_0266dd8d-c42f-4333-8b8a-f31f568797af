<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import SolarUserRoundedBoldDuotone from '~icons/solar/user-rounded-bold-duotone';
	import SolarUsersGroupRoundedBoldDuotone from '~icons/solar/users-group-rounded-bold-duotone';
	import SolarUsersGroupTwoRoundedBoldDuotone from '~icons/solar/users-group-two-rounded-bold-duotone';
	import IconoirNavArrowRight from '~icons/iconoir/nav-arrow-right';

	const tabsContentPersonal = $state([
		{
			name: '1 year',
			price: '$16',
			pricePerMonth: '$1.3',
			discount: '-33%'
		},
		{
			name: '6 months',
			price: '$10.2',
			pricePerMonth: '$1.7',
			discount: '-15%'
		},
		{
			name: '3 months',
			price: '$5.4',
			pricePerMonth: '$1.8',
			discount: '-10%'
		},
		{
			name: '1 month',
			price: '$2',
			pricePerMonth: '',
			discount: 'no discount'
		}
	]);

	const tabs = $state([
		{
			trigger: { name: 'Personal', icon: SolarUserRoundedBoldDuotone },
			content: tabsContentPersonal
		},
		{
			trigger: { name: 'Shared', icon: SolarUsersGroupRoundedBoldDuotone },
			content: 'This is the profile content.'
		},
		{
			trigger: { name: 'Family', icon: SolarUsersGroupTwoRoundedBoldDuotone },
			content: 'These are the settings.'
		}
	]);

	let activeTab = $state(0);

	function selectTab(index: number) {
		activeTab = index;
	}
</script>

<div class="mx-auto max-w-screen-sm">
	<div class="relative right-0">
		<ul
			class="relative flex list-none flex-wrap space-x-1 rounded-lg bg-white px-1.5 py-1.5"
			role="list"
		>
			{#each tabs as tab, i}
				<li class="z-30 flex-auto text-center">
					<button
						onclick={() => selectTab(i)}
						class={twMerge(
							'z-30 mb-0 flex w-full cursor-pointer items-center justify-center gap-2 rounded-md border-0 bg-inherit px-4 py-2 text-sm font-medium text-slate-600 transition-colors ease-in-out hover:bg-slate-100',
							activeTab === i && '!bg-slate-900 text-white shadow-sm'
						)}
						role="tab"
						aria-selected={activeTab === i}
					>
						{#if tab.trigger.icon}
							<tab.trigger.icon width="24" height="24" />
						{/if}
						<span>{tab.trigger.name}</span>
					</button>
				</li>
			{/each}
		</ul>
	</div>

	<div class="my-4">
		{#each tabs as tab, i}
			{#if activeTab === i}
				{#if typeof tab.content === 'string'}
					<div class="rounded-md bg-white p-4 shadow-sm">
						{tab.content}
					</div>
				{:else}
					<div class="flex flex-col gap-2">
						{#each tab.content as item}
							<div
								class="flex justify-between rounded-xl bg-white px-4 py-3 transition-[background,_box-shadow] hover:bg-slate-50 hover:shadow-sm"
							>
								<div class="flex flex-col items-start gap-1 text-gray-600">
									{#if item.discount}
										<p
											class={twMerge(
												'rounded-full  bg-yellow-300 px-2 py-0.5 text-sm font-medium',
												item.discount === 'no discount' && 'bg-gray-300 text-gray-600'
											)}
										>
											{item.discount}
										</p>
									{/if}
									<div class="text-lg font-semibold">{item.name}</div>
								</div>
								<div class="flex items-center justify-center gap-4">
									<div class="flex flex-col items-end">
										<div class="text-lg font-semibold">{item.price}</div>

										{#if item.pricePerMonth}
											<div class="text-xs font-medium text-slate-500">
												{item.pricePerMonth}/month
											</div>
										{/if}
									</div>
									<IconoirNavArrowRight class="text-slate-400" width="24" height="24" />
								</div>
							</div>
						{/each}
					</div>
				{/if}
			{/if}
		{/each}
	</div>

	<div class="flex flex-col justify-between rounded-md bg-white p-6 shadow-sm">
		<h3 class="mb-4 text-lg font-semibold text-slate-700">All Plans Include</h3>
		<ul
			class="list-inside list-disc list-image-[url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%20-6%2024%2024%22%3E%3Cpath%20fill%3D%22none%22%20stroke%3D%22currentColor%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20stroke-width%3D%221.5%22%20d%3D%22m5%2013l4%204L19%207%22%20%2F%3E%3C%2Fsvg%3E)] space-y-1 text-gray-700"
		>
			<li>One subscription for all your devices</li>
			<li>Unlimited traffic. Maximum speed</li>
			<li>Locations across all continents</li>
			<li>Live support available 24/7</li>
		</ul>
	</div>
</div>
