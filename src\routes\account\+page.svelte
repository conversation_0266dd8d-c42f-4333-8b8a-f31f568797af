<script>
	import Sidebar from '@lib/components/navigation/Sidebar.svelte';
	import TabNavigation from '@lib/components/navigation/TabNavigation.svelte';
	import { m } from '@lib/paraglide/messages';
	import { <PERSON><PERSON>, Button, Card, Heading, Input, Label, Spinner } from 'flowbite-svelte';
	import { onMount } from 'svelte';

	import SolarUserCircleLineDuotone from '~icons/solar/user-circle-line-duotone';
	import SolarCardLineDuotone from '~icons/solar/card-line-duotone';
	import SolarClipboardListLineDuotone from '~icons/solar/clipboard-list-line-duotone';

	let { userInfo = null, userCards = [], userOrders = [], loading = true, error = null } = $props();

	onMount(async () => {
		try {
			// Fetch user information
			const userResponse = await fetch('/api/users/me');
			if (userResponse.ok) {
				userInfo = await userResponse.json();
			}

			// Fetch user cards
			const cardsResponse = await fetch('/api/cards/my');
			if (cardsResponse.ok) {
				userCards = await cardsResponse.json();
			}

			// Fetch user orders
			const ordersResponse = await fetch('/api/orders/my');
			if (ordersResponse.ok) {
				userOrders = await ordersResponse.json();
			}
		} catch (err) {
			error = err.message;
		} finally {
			loading = false;
		}
	});

	async function deleteCard(cardId) {
		try {
			const response = await fetch(`/api/cards/${cardId}`, {
				method: 'DELETE'
			});
			if (response.ok) {
				userCards = userCards.filter((card) => card.id !== cardId);
			}
		} catch (err) {
			error = err.message;
		}
	}

	function formatCardType(type) {
		return type === 'bank_card' ? 'Bank Card' : 'SBP';
	}

	function formatOrderState(state) {
		const states = {
			pending: 'Pending',
			canceled: 'Canceled',
			paid: 'Paid',
			refunded: 'Refunded',
			part_refunded: 'Partially Refunded'
		};
		return states[state] || state;
	}

	function getOrderStateColor(state) {
		const colors = {
			pending: 'yellow',
			canceled: 'red',
			paid: 'green',
			refunded: 'blue',
			part_refunded: 'purple'
		};
		return colors[state] || 'gray';
	}
</script>

<div class="container mb-6 flex h-full w-full max-w-screen-2xl flex-col gap-10 pt-10 lg:flex-row">
	<Sidebar></Sidebar>
	<div class="relative w-full">
		<div class="mx-auto space-y-6">
			<!-- {#if loading}
				<div class="flex justify-center">
					<Spinner size="8" />
				</div>
			{:else if error}
				<Alert color="red" class="mb-4">
					<span class="font-medium">Error:</span>
					{error}
				</Alert>
			{:else} -->
				<!-- User Information Section -->
				<div class="flex flex-col justify-between rounded-md bg-white p-6 shadow-sm">
					<div class="mb-4 flex items-center gap-3">
						<SolarUserCircleLineDuotone width="24" height="24" />
						<Heading tag="h2" class="text-xl font-semibold">User Information</Heading>
					</div>
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div>
							<Label class="mb-1 block text-sm font-medium">User ID</Label>
							<Input value={userInfo?.id || 'N/A'} readonly />
						</div>
						<div>
							<Label class="mb-1 block text-sm font-medium">Email</Label>
							<Input value={userInfo?.email || 'Not provided'} readonly />
						</div>
						<div>
							<Label class="mb-1 block text-sm font-medium">Telegram ID</Label>
							<Input value={userInfo?.telegram_id || 'Not connected'} readonly />
						</div>
					</div>
				</div>

				<!-- Payment Methods Section -->
				<div class="flex flex-col justify-between rounded-md bg-white p-6 shadow-sm">
					<div class="mb-4 flex items-center gap-3">
						<SolarCardLineDuotone width="24" height="24" />
						<Heading tag="h2" class="text-xl font-semibold">Payment Methods</Heading>
					</div>
					{#if userCards.length === 0}
						<Alert color="blue" class="mb-4">
							<span class="font-medium">No payment methods found.</span>
						</Alert>
					{:else}
						<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
							{#each userCards as card}
								<Card class="border border-gray-200 p-4">
									<div class="mb-2 flex items-start justify-between">
										<div>
											<p class="font-medium">{formatCardType(card.type)}</p>
											<p class="text-sm text-gray-600">**** {card.last4}</p>
										</div>
										<Button size="xs" color="red" outline on:click={() => deleteCard(card.id)}>
											Delete
										</Button>
									</div>
									<div class="text-sm text-gray-500">
										<p>Issuer: {card.issuer_name || 'Unknown'}</p>
										<p>Type: {card.card_type || 'Unknown'}</p>
									</div>
								</Card>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Orders History Section -->
				<div class="flex flex-col justify-between rounded-md bg-white p-6 shadow-sm">
					<div class="mb-4 flex items-center gap-3">
						<SolarClipboardListLineDuotone width="24" height="24" />
						<Heading tag="h2" class="text-xl font-semibold">Orders History</Heading>
					</div>
					{#if userOrders.length === 0}
						<Alert color="blue" class="mb-4">
							<span class="font-medium">No orders found.</span>
						</Alert>
					{:else}
						<div class="space-y-4">
							{#each userOrders as order}
								<Card class="border border-gray-200 p-4">
									<div class="mb-2 flex items-start justify-between">
										<div>
											<p class="font-medium">Order #{order.id.slice(-8)}</p>
											<p class="text-sm text-gray-600">Tariff ID: {order.tariff_id.slice(-8)}</p>
										</div>
										<Alert color={getOrderStateColor(order.state)} class="p-2">
											<span class="text-xs font-medium">{formatOrderState(order.state)}</span>
										</Alert>
									</div>
									<div class="text-sm text-gray-500">
										{#if order.sub_id}
											<p>Subscription: {order.sub_id.slice(-8)}</p>
										{/if}
										{#if order.promocode_id}
											<p>Promo Code: {order.promocode_id.slice(-8)}</p>
										{/if}
									</div>
									{#if order.payment_links && order.payment_links.length > 0}
										<div class="mt-3 flex flex-wrap gap-2">
											{#each order.payment_links as link}
												<Button size="xs" color="blue" outline href={link.url} target="_blank">
													Pay via {link.provider}
												</Button>
											{/each}
										</div>
									{/if}
								</Card>
							{/each}
						</div>
					{/if}
				</div>
			<!-- {/if} -->
		</div>
	</div>
	<!-- <div class="h-[900px] w-full bg-amber-400"></div> -->
</div>
<TabNavigation></TabNavigation>
