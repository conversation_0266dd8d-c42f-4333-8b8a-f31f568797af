<script lang="ts">
	import { Heading, Button } from 'flowbite-svelte';
	import { twJoin, twMerge } from 'tailwind-merge';
	import { m } from '@lib/paraglide/messages';

	import SolarShieldCheckBoldDuotone from '~icons/solar/shield-check-bold-duotone';
	import SolarShieldCrossBoldDuotone from '~icons/solar/shield-cross-bold-duotone';

	// "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
	// "type": "promo",
	// "tariff_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
	// "auto_renew": true,
	// "card_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
	// "expire": 0,
	// "active": true

	interface Props {
		type?: string;
		autoRenew?: boolean;
		active?: boolean;
		expire?: number;
		expire_at?: Date; // Explicitly type as Date
	}

	let {
		type = 'regular',
		autoRenew = false,
		active = false,
		expire = 0,
		expire_at = new Date('01 01 2026 00:00 UTC')
	}: Props = $props();

	function calculateTimeUntilExpiry(expire_at: Date) {
		const now = new Date();
		const expiry = expire_at;

		if (expiry <= now) {
			return { months: 0, days: 0, hours: 0, isExpired: true };
		}

		const diffMs = +expiry - +now;
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
		const months = Math.floor(diffDays / 30);
		const days = diffDays % 30;
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60)) % 24;

		return { months, days, hours: diffHours, isExpired: false };
	}

	// Calculate time until expiry
	const timeUntilExpiry = calculateTimeUntilExpiry(expire_at);
</script>

{#if active}
	<!--  Active -->
	<div
		class={twJoin(
			'relative mx-auto flex w-full max-w-screen-sm flex-col gap-4 rounded-2xl border border-emerald-100 bg-white bg-gradient-to-r from-emerald-50/50 via-transparent via-30% to-emerald-100 p-4 shadow-sm/5 md:flex-row lg:p-6 dark:bg-gray-800 dark:from-emerald-900 dark:to-emerald-800',
			autoRenew ? 'md:items-center' : 'items-start'
		)}
	>
		<SolarShieldCheckBoldDuotone
			class="h-auto w-[5rem] shrink-0 text-green-500/80"
			width="100%"
			height="100%"
		/>

		<div class="flex flex-col items-start gap-4">
			<div class="flex flex-col gap-1">
				<Heading tag="h2" class="text-lg font-semibold text-emerald-700 dark:text-white "
					>{m['subscription.active']()}</Heading
				>
				<p class="text-sm text-gray-600">
					{m['subscription.expires_on']()} <strong
						>{expire_at.toLocaleDateString(undefined, {
							year: 'numeric',
							month: 'short',
							day: 'numeric'
						})}</strong
					>
					{#if !timeUntilExpiry.isExpired}
						{m['subscription.in']()}
						<strong>
							{timeUntilExpiry.months ? timeUntilExpiry.months + ' ' + m['subscription.months']() : ''}
							{timeUntilExpiry.days ? timeUntilExpiry.days + ' ' + m['subscription.days']() : ''}
							{timeUntilExpiry.hours ? timeUntilExpiry.hours + ' ' + m['subscription.hours']() : ''}
						</strong>
					{/if}
				</p>
			</div>

			{#if !autoRenew}
				<Button color="light" class="px-3 py-2 shadow-xs">{m['subscription.renew']()}</Button>
			{/if}
		</div>
	</div>
{:else}
	<!-- Expired -->
	<div
		class="relative mx-auto flex w-full max-w-screen-sm flex-col items-start gap-4 rounded-2xl border border-slate-100 bg-white bg-gradient-to-r from-slate-50/50 via-transparent via-30% to-slate-200 p-4 shadow-sm/5 md:flex-row lg:p-6 dark:bg-gray-800 dark:from-slate-900 dark:to-slate-800"
	>
		<SolarShieldCrossBoldDuotone
			class="h-auto w-[5rem] shrink-0 text-slate-500/80"
			width="100%"
			height="100%"
		/>

		<div class="flex flex-col items-start gap-4">
			<div class="flex flex-col gap-1">
				<Heading tag="h2" class="text-lg font-semibold text-slate-700 dark:text-white "
					>{m['subscription.inactive']()}</Heading
				>
				<p class="text-sm text-gray-600">
					{m['subscription.inactive_description']()}
				</p>
			</div>
			<Button color="dark" class="px-3 py-2 shadow-xs">{m['subscription.renew']()}</Button>
		</div>
	</div>
{/if}
