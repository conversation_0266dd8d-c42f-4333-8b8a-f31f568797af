<script lang="ts">
	import { m } from '@lib/paraglide/messages';
	import { setLocale, locales, getLocale } from '@lib/paraglide/runtime';
	import { Button, Modal, Label, Input, Checkbox } from 'flowbite-svelte';
	import { fade } from 'svelte/transition';
	import { twMerge } from 'tailwind-merge';
	let { modalState = true, onClose } = $props();

	import SolarCheckCircleBoldDuotone from '~icons/solar/check-circle-bold-duotone';

	const languages: { name: string; code: (typeof locales)[number] }[] = [
		{ name: 'English', code: 'en' },
		{ name: 'Русский', code: 'ru' }
	];

	function handleClose() {
		modalState = false;
		onClose?.();
	}

	// console log modalState on change
	$effect(() => {
		console.log('modalState', modalState);
	});
</script>

<Modal
	title={m['sidebar.choose_language']()}
	size="xs"
	bind:open={modalState}
	onclose={handleClose}
	transition={fade}
	params={{ duration: 200 }}
	class="backdrop:bg-gray-900/40"
>
	<ul class="my-4 space-y-3">
		{#each languages as language}
			{@const isActive = getLocale() === language.code}
			<li>
				<button
					onclick={() => setLocale(language.code)}
					disabled={isActive}
					class={twMerge(
						'group flex w-full rounded-lg bg-gray-50 p-3 text-start text-base text-gray-900 hover:bg-gray-100 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500',
						isActive && '!bg-purple-50'
					)}
				>
					<span class="ms-3 flex-1 whitespace-nowrap" class:text-purple-700={isActive}
						>{language.name}</span
					>
					{#if isActive}
						<SolarCheckCircleBoldDuotone class="text-purple-500" width="24" height="24" />
					{/if}
				</button>
			</li>
		{/each}
	</ul>
</Modal>
