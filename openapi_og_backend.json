{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/api/auth/send": {"post": {"tags": ["auth"], "summary": "Send Email", "operationId": "send_email_api_auth_send_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailRequestDto"}}}, "required": true}, "responses": {"200": {"description": "Mail was sent to user's email"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/auth": {"post": {"tags": ["auth"], "summary": "Get Token", "operationId": "get_token_api_auth_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthRequestDto"}}}, "required": true}, "responses": {"200": {"description": "JSON with JWT", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/users/me": {"get": {"tags": ["users"], "summary": "Get Current User", "operationId": "get_current_user_api_users_me_get", "responses": {"200": {"description": "Returns current user object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}}}, "/api/subs/my": {"get": {"tags": ["subs"], "summary": "Get Current User Subs", "operationId": "get_current_user_subs_api_subs_my_get", "responses": {"200": {"description": "Returns user's subs", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SubResponseDto"}, "type": "array", "title": "Response 200 Get Current User Subs Api Subs My Get"}}}}}}}, "/api/subs/{sub_id}": {"get": {"tags": ["subs"], "summary": "Get Sub By Id", "operationId": "get_sub_by_id_api_subs__sub_id__get", "parameters": [{"name": "sub_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Sub Id"}}], "responses": {"200": {"description": "Returns user's sub with specific id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubResponseDto"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/orders/my": {"get": {"tags": ["orders"], "summary": "Get Current User Orders", "operationId": "get_current_user_orders_api_orders_my_get", "responses": {"200": {"description": "Returns current user's orders", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderResponseDto"}, "type": "array", "title": "Response Get Current User Orders Api Orders My Get"}}}}}}}, "/api/orders": {"post": {"tags": ["orders"], "summary": "Create Order", "operationId": "create_order_api_orders_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCreateRequestDto"}}}, "required": true}, "responses": {"200": {"description": "Returns created order", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseDto"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/orders/{order_id}": {"get": {"tags": ["orders"], "summary": "Get Order By Id", "operationId": "get_order_by_id_api_orders__order_id__get", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Order Id"}}], "responses": {"200": {"description": "Returns user's order with specific id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseDto"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tariffs": {"get": {"tags": ["tariffs"], "summary": "Get Tariffs", "operationId": "get_tariffs_api_tariffs_get", "responses": {"200": {"description": "Returns tariffs", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/TariffResponseDto"}, "type": "array", "title": "Response Get Tariffs Api Tariffs Get"}}}}}}}, "/api/cards/my": {"get": {"tags": ["cards"], "summary": "Get Users Cards", "operationId": "get_users_cards_api_cards_my_get", "responses": {"200": {"description": "Returns user's payment methods", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CardResponseDto"}, "type": "array", "title": "Response Get Users Cards Api Cards My Get"}}}}}}}, "/api/cards/{card_id}": {"delete": {"tags": ["cards"], "summary": "Delete Card", "operationId": "delete_card_api_cards__card_id__delete", "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AuthRequestDto": {"properties": {"token": {"type": "string", "title": "Token"}}, "type": "object", "required": ["token"], "title": "AuthRequestDto"}, "AuthResponseDto": {"properties": {"token": {"type": "string", "title": "Token"}, "type": {"$ref": "#/components/schemas/TokenType"}}, "type": "object", "required": ["token", "type"], "title": "AuthResponseDto"}, "BankCardType": {"type": "string", "enum": ["mastercard", "visa", "mir", "unionpay", "unknown"], "title": "BankCardType"}, "CardResponseDto": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/CardType"}, "last4": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last4"}, "issuer_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issuer Name"}, "card_type": {"anyOf": [{"$ref": "#/components/schemas/BankCardType"}, {"type": "null"}]}}, "type": "object", "required": ["id", "type", "last4", "issuer_name", "card_type"], "title": "CardResponseDto"}, "CardType": {"type": "string", "enum": ["bank_card", "sbp"], "title": "CardType"}, "Currency": {"type": "string", "enum": ["rub", "usd", "stars"], "title": "<PERSON><PERSON><PERSON><PERSON>"}, "DurationType": {"type": "string", "enum": ["hours", "days", "months"], "title": "DurationType"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "OrderCreateRequestDto": {"properties": {"tariff_id": {"type": "string", "format": "uuid", "title": "Tariff Id"}, "promocode_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Promocode Id"}, "sub_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Sub Id"}}, "type": "object", "required": ["tariff_id", "promocode_id", "sub_id"], "title": "OrderCreateRequestDto"}, "OrderResponseDto": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "sub_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Sub Id"}, "tariff_id": {"type": "string", "format": "uuid", "title": "Tariff Id"}, "promocode_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Promocode Id"}, "state": {"$ref": "#/components/schemas/OrderState"}, "payment_links": {"items": {"$ref": "#/components/schemas/PaymentLinkDto"}, "type": "array", "title": "Payment Links"}}, "type": "object", "required": ["id", "sub_id", "tariff_id", "promocode_id", "state", "payment_links"], "title": "OrderResponseDto"}, "OrderState": {"type": "string", "enum": ["pending", "canceled", "paid", "refunded", "part_refunded"], "title": "OrderState"}, "PaymentLinkDto": {"properties": {"provider": {"$ref": "#/components/schemas/PaymentProvider"}, "url": {"type": "string", "title": "Url"}}, "type": "object", "required": ["provider", "url"], "title": "PaymentLinkDto"}, "PaymentProvider": {"type": "string", "enum": ["yookassa_cards", "yookassa_sbp", "cryptobot"], "title": "PaymentProvider"}, "PriceResponseDto": {"properties": {"currency": {"$ref": "#/components/schemas/Currency"}, "value": {"type": "string", "title": "Value"}}, "type": "object", "required": ["currency", "value"], "title": "PriceResponseDto"}, "SendEmailRequestDto": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "tariff_line_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tariff Line Id"}}, "type": "object", "required": ["email"], "title": "SendEmailRequestDto"}, "SubResponseDto": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/SubscriptionType"}, "tariff_id": {"type": "string", "format": "uuid", "title": "Tariff Id"}, "auto_renew": {"type": "boolean", "title": "Auto Renew"}, "card_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Card Id"}, "expire": {"type": "integer", "title": "Expire"}, "active": {"type": "boolean", "title": "Active"}}, "type": "object", "required": ["id", "type", "tariff_id", "auto_renew", "card_id", "expire", "active"], "title": "SubResponseDto"}, "SubscriptionType": {"type": "string", "enum": ["promo", "regular"], "title": "SubscriptionType"}, "TariffResponseDto": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "title": {"type": "string", "minLength": 5, "title": "Title"}, "duration": {"type": "integer", "title": "Duration"}, "duration_type": {"$ref": "#/components/schemas/DurationType"}, "prices": {"anyOf": [{"items": {"$ref": "#/components/schemas/PriceResponseDto"}, "type": "array"}, {"type": "null"}], "title": "Prices"}}, "type": "object", "required": ["id", "title", "duration", "duration_type", "prices"], "title": "TariffResponseDto"}, "TokenType": {"type": "string", "enum": ["bearer"], "title": "TokenType"}, "UserResponseDto": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "telegram_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Telegram Id"}}, "type": "object", "required": ["id"], "title": "UserResponseDto"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}