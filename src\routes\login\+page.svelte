<script>
	import Icon from '@iconify/svelte';
	import Button from '@lib/components/ui/Button.svelte';
</script>

<svelte:head>
	<title>Login</title>
</svelte:head>

<div class="container mx-auto flex max-w-screen-sm flex-col items-center gap-4 min-h-screen justify-center">
	<div class="w-full rounded-2xl bg-white p-4 py-10">
		<h1 class="text-center text-2xl font-semibold mb-10">Login or create account</h1>

		<!-- Login with email magic link -->
		<form action="get" class="mx-auto max-w-80 space-y-2">
			<input type="email" placeholder="Email" class="w-full rounded-lg border-slate-300" />
			<Button type="submit" class="w-full">Send me a login link</Button>
		</form>
		<hr class="my-6 border-gray-200" />
		<!-- Login with Google, Telegram -->
		<div class="max-w-80 mx-auto">
			<h2 class="text-center text-sm font-medium text-slate-600">or login with</h2>
			<ul class="mt-4 space-y-2">
				<li>
					<Button href="" variant="secondary" class="w-full" icon={true} iconPosition="left">
						Google
						{#snippet iconSnippet()}
							<Icon icon="logos:google-icon" width="24" height="24" />
						{/snippet}
					</Button>
				</li>
				<li>
					<Button href="" variant="secondary" class="w-full" icon={true} iconPosition="left">
						Telegram
						{#snippet iconSnippet()}
							<Icon icon="logos:telegram" width="24" height="24" />
						{/snippet}
					</Button>
				</li>
			</ul>
		</div>
	</div>
</div>
