import { m } from '@lib/paraglide/messages';

import IconoirHomeSimpleDoor from '~icons/iconoir/home-simple-door';
import IconoirTools from '~icons/iconoir/tools';
import IconoirProfileCircle from '~icons/iconoir/profile-circle';
import IconoirHelpSquare from '~icons/iconoir/help-square';
import IconoirChatBubbleTranslate from '~icons/iconoir/chat-bubble-translate';


export const languageModal = $state({
	state: false
});

export const menuItems = [
	{
		href: '/#',
		icon: IconoirHomeSimpleDoor,
		label: m['sidebar.home']()
	},
	{
		href: '/#',
		icon: IconoirTools,
		label: m['sidebar.install_vpn']()
	},
	{
		href: '/account',
		icon: IconoirProfileCircle,
		label: m['sidebar.account']()
	},
	{
		href: '/components/sidebar',
		icon: IconoirHelpSquare,
		label: m['sidebar.support']()
	},
	{
		type: 'divider',
		class: 'my-2 border-t border-gray-200 dark:border-gray-700'
	},
	{
		type: 'button',
		icon: IconoirChatBubbleTranslate,
		label: m['sidebar.language'](),
		onClick: () => (languageModal.state = true)
	}
];
