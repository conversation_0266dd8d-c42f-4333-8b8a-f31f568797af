@import 'tailwindcss';

@plugin 'flowbite/plugin';
@source "../node_modules/flowbite-svelte/dist";

@font-face {
	font-family: Inter;
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/fonts/Inter-Regular.woff2') format('woff2');
}

@font-face {
	font-family: Inter;
	font-style: normal;
	font-weight: 500;
	font-display: swap;
	src: url('/fonts/Inter-Medium.woff2') format('woff2');
}

@font-face {
	font-family: Inter;
	font-style: normal;
	font-weight: 600;
	font-display: swap;
	src: url('/fonts/Inter-Bold.woff2') format('woff2');
}

@font-face {
	font-family: Inter;
	font-style: normal;
	font-weight: 700;
	font-display: swap;
	src: url('/fonts/Inter-Black.woff2') format('woff2');
}

@custom-variant dark (&:where(.dark, .dark *));

@theme {
	--font-sans:
		'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
		'Segoe UI Symbol', 'Noto Color Emoji';

	--color-primary-50: var(--color-purple-50);
	--color-primary-100: var(--color-purple-100);
	--color-primary-200: var(--color-purple-200);
	--color-primary-300: var(--color-purple-300);
	--color-primary-400: var(--color-purple-400);
	--color-primary-500: var(--color-purple-500);
	--color-primary-600: var(--color-purple-600);
	--color-primary-700: var(--color-purple-700);
	--color-primary-800: var(--color-purple-800);
	--color-primary-900: var(--color-purple-900);

	--color-secondary-50: #f0f9ff;
	--color-secondary-100: #e0f2fe;
	--color-secondary-200: #bae6fd;
	--color-secondary-300: #7dd3fc;
	--color-secondary-400: #38bdf8;
	--color-secondary-500: #0ea5e9;
	--color-secondary-600: #0284c7;
	--color-secondary-700: #0369a1;
	--color-secondary-800: #075985;
	--color-secondary-900: #0c4a6e;
}

@layer base {
	/* disable chrome cancel button */
	input[type='search']::-webkit-search-cancel-button {
		display: none;
	}

	button {
		@apply cursor-pointer;
	}

	strong {
		@apply font-semibold;
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 1rem;

	@variant sm {
		max-width: none;
	}

	@variant md {
		max-width: none;
	}

	@variant lg {
		max-width: none;
		padding-inline: 2rem;
	}

	@variant xl {
		max-width: none;
		padding-inline: 2rem;
	}

	@variant 2xl {
		max-width: none;
		padding-inline: 2rem;
	}
}
