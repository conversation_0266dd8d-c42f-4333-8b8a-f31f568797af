<script>
	import Plans from '@lib/components/Plans.svelte';
	import Sidebar from '@lib/components/navigation/Sidebar.svelte';
	import TabNavigation from '@lib/components/navigation/TabNavigation.svelte';
	import Subscription from '@lib/components/subscription/Subscription.svelte';
	import { m } from '@lib/paraglide/messages';
	import { <PERSON><PERSON>, <PERSON><PERSON>, Heading } from 'flowbite-svelte';

	import SolarShieldWarningLineDuotone from '~icons/solar/shield-warning-line-duotone';
</script>

<div class="container flex w-full max-w-screen-2xl flex-col gap-10 pt-10 mb-6 lg:flex-row">
	<Sidebar></Sidebar>
	<div class="relative w-full">
		<Alert
			color="secondary"
			class="mx-auto mb-4 flex w-fit items-center justify-center gap-2 rounded-full bg-slate-200 py-2"
		>
			<SolarShieldWarningLineDuotone
				class="inline-block shrink-0"
				width="24"
				height="24"
			/>
			<span class="font-medium">{m['common.not-protected']()}</span>
		</Alert>

		<div class="mx-auto space-y-4">
			<div class="flex flex-col gap-2">
				<Subscription active={true}></Subscription>
				<Subscription active={true} autoRenew></Subscription>
				<Subscription></Subscription>
			</div>
			<Plans></Plans>
		</div>
	</div>
</div>
<TabNavigation></TabNavigation>
